import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import "../public/iconfont/iconfont.css"
import "../public/iconfont/iconfont.js"
import "@/assets/css/iconfont-symbol.css"
// Element Plus
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";

const app = createApp(App);

// Register Element Plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.use(store).use(router).use(ElementPlus).mount("#app");
